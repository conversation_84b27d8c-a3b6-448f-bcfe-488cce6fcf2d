package me.darkness.wallet;

import me.darkness.wallet.commands.AdminWalletCommand;
import me.darkness.wallet.commands.PortfelCommand;
import me.darkness.wallet.config.ConfigManager;
import me.darkness.wallet.database.DatabaseManager;
import me.darkness.wallet.listeners.GuiListener;
import me.darkness.wallet.managers.DailyRewardManager;
import me.darkness.wallet.managers.ProductManager;
import me.darkness.wallet.managers.WalletManager;
import me.darkness.wallet.placeholders.WalletPlaceholder;
import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;

public final class Main extends JavaPlugin {

    private static Main instance;
    private ConfigManager configManager;
    private DatabaseManager databaseManager;
    private WalletManager walletManager;
    private ProductManager productManager;
    private DailyRewardManager dailyRewardManager;

    @Override
    public void onEnable() {
        instance = this;

        configManager = new ConfigManager(this);
        configManager.loadConfigs();

        databaseManager = new DatabaseManager(this);
        databaseManager.initialize();

        walletManager = new WalletManager(this);
        productManager = new ProductManager(this);
        dailyRewardManager = new DailyRewardManager(this);

        registerCommands();
        getServer().getConsoleSender().sendMessage("§8[§a§l777-Portfel§8] §f§nZarejestrowano komendy!§r.");
        registerListeners();
        registerPlaceholders();
        getServer().getConsoleSender().sendMessage("§8[§a§l777-Portfel§8] §f§nZarejestrowano placeholdery!§r.");

        getServer().getConsoleSender().sendMessage("§8[§a§l777-Portfel§8] §aUruchomiono plugin!§r.");
    }

    @Override
    public void onDisable() {
        if (databaseManager != null) {
            databaseManager.close();
        }
        getServer().getConsoleSender().sendMessage("§8[§4§l777-Portfel§8] §cWylaczono plugin :C§r.");
    }

    private void registerCommands() {
        getCommand("portfel").setExecutor(new PortfelCommand(this));

        AdminWalletCommand adminCommand = new AdminWalletCommand(this);
        getCommand("adminwallet").setExecutor(adminCommand);
        getCommand("adminwallet").setTabCompleter(adminCommand);
    }

    private void registerListeners() {
        Bukkit.getPluginManager().registerEvents(new GuiListener(this), this);
    }

    private void registerPlaceholders() {
        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            new WalletPlaceholder(this).register();
        }
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }

    public WalletManager getWalletManager() {
        return walletManager;
    }

    public ProductManager getProductManager() {
        return productManager;
    }

    public DailyRewardManager getDailyRewardManager() {
        return dailyRewardManager;
    }
}
