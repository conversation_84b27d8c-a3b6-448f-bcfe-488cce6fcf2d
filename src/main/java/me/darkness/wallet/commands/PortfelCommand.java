package me.darkness.wallet.commands;

import me.darkness.wallet.Main;
import me.darkness.wallet.gui.WalletGui;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class PortfelCommand implements CommandExecutor {
    
    private final Main plugin;
    
    public PortfelCommand(Main plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Ta komenda jest dostepna tylko dla graczy!");
            return true;
        }
        
        Player player = (Player) sender;
        WalletGui gui = new WalletGui(plugin, player);
        gui.open();
        
        return true;
    }
}
