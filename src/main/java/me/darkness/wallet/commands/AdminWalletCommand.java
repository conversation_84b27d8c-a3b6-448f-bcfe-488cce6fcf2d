package me.darkness.wallet.commands;

import me.darkness.wallet.Main;
import me.darkness.wallet.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class AdminWalletCommand implements CommandExecutor, TabCompleter {
    
    private final Main plugin;
    
    public AdminWalletCommand(Main plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("wallet.admin")) {
            MessageUtils.sendMessage(sender, plugin.getConfigManager().getConfig("messages.yml")
                    .getString("no-permission", "&c<PERSON>ie masz uprawnien!"));
            return true;
        }
        
        if (args.length == 1 && args[0].equalsIgnoreCase("reload")) {
            plugin.getConfigManager().reloadAllConfigs();
            plugin.getProductManager().loadProducts();
            plugin.getDailyRewardManager().reloadData();
            MessageUtils.sendMessage(sender, plugin.getConfigManager().getConfig("messages.yml")
                    .getString("admin-config-reloaded", "&aKonfiguracja zostala przeladowana!"));
            return true;
        }

        if (args.length < 3) {
            MessageUtils.sendMessage(sender, plugin.getConfigManager().getConfig("messages.yml")
                    .getString("admin-usage", "&cUzycie: /adminwallet <add|take|set|reload> <gracz> <ilosc>"));
            return true;
        }
        
        String action = args[0].toLowerCase();
        String playerName = args[1];
        
        double amount;
        try {
            amount = Double.parseDouble(args[2]);
        } catch (NumberFormatException e) {
            MessageUtils.sendMessage(sender, plugin.getConfigManager().getConfig("messages.yml")
                    .getString("admin-invalid-amount", "&cNieprawidlowa ilosc!"));
            return true;
        }

        if (amount < 0) {
            MessageUtils.sendMessage(sender, plugin.getConfigManager().getConfig("messages.yml")
                    .getString("admin-negative-amount", "&cIlosc nie moze byc ujemna!"));
            return true;
        }

        OfflinePlayer target = Bukkit.getOfflinePlayer(playerName);
        if (!target.hasPlayedBefore() && !target.isOnline()) {
            MessageUtils.sendMessage(sender, plugin.getConfigManager().getConfig("messages.yml")
                    .getString("player-not-found", "&cGracz nie zostal znaleziony!"));
            return true;
        }
        
        switch (action) {
            case "add":
                plugin.getWalletManager().addBalance(target.getUniqueId(), amount).thenRun(() -> {
                    MessageUtils.sendMessage(sender, plugin.getConfigManager().getConfig("messages.yml")
                            .getString("admin-balance-added", "&aDodano {AMOUNT} VPLN graczowi {PLAYER}!")
                            .replace("{AMOUNT}", plugin.getWalletManager().formatBalance(amount))
                            .replace("{PLAYER}", target.getName()));
                    if (target.isOnline()) {
                        Player onlineTarget = target.getPlayer();
                        MessageUtils.sendMessage(onlineTarget, plugin.getConfigManager().getConfig("messages.yml")
                                .getString("balance-added", "&aDodano {AMOUNT} VPLN do twojego portfela!")
                                .replace("{AMOUNT}", plugin.getWalletManager().formatBalance(amount)));
                    }
                });
                break;
                
            case "take":
            case "remove":
                plugin.getWalletManager().removeBalance(target.getUniqueId(), amount).thenRun(() -> {
                    MessageUtils.sendMessage(sender, plugin.getConfigManager().getConfig("messages.yml")
                            .getString("admin-balance-removed", "&aOdjeto {AMOUNT} VPLN graczowi {PLAYER}!")
                            .replace("{AMOUNT}", plugin.getWalletManager().formatBalance(amount))
                            .replace("{PLAYER}", target.getName()));
                    if (target.isOnline()) {
                        Player onlineTarget = target.getPlayer();
                        MessageUtils.sendMessage(onlineTarget, plugin.getConfigManager().getConfig("messages.yml")
                                .getString("balance-removed", "&cOdjeto {AMOUNT} VPLN z twojego portfela!")
                                .replace("{AMOUNT}", plugin.getWalletManager().formatBalance(amount)));
                    }
                });
                break;
                
            case "set":
                plugin.getWalletManager().setBalance(target.getUniqueId(), amount).thenRun(() -> {
                    MessageUtils.sendMessage(sender, plugin.getConfigManager().getConfig("messages.yml")
                            .getString("admin-balance-set", "&aUstawiono {AMOUNT} VPLN graczowi {PLAYER}!")
                            .replace("{AMOUNT}", plugin.getWalletManager().formatBalance(amount))
                            .replace("{PLAYER}", target.getName()));
                    if (target.isOnline()) {
                        Player onlineTarget = target.getPlayer();
                        MessageUtils.sendMessage(onlineTarget, plugin.getConfigManager().getConfig("messages.yml")
                                .getString("balance-set", "&eUstawiono twoj portfel na {AMOUNT} VPLN!")
                                .replace("{AMOUNT}", plugin.getWalletManager().formatBalance(amount)));
                    }
                });
                break;

            default:
                MessageUtils.sendMessage(sender, plugin.getConfigManager().getConfig("messages.yml")
                        .getString("admin-invalid-command", "&cNieprawidlowa komenda! Uzyj: add, take, set, reload"));
                break;
        }
        
        return true;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            completions.addAll(Arrays.asList("add", "take", "set", "reload"));
        } else if (args.length == 2 && !args[0].equalsIgnoreCase("reload")) {
            for (Player player : Bukkit.getOnlinePlayers()) {
                completions.add(player.getName());
            }
        } else if (args.length == 3) {
            completions.addAll(Arrays.asList("100", "500", "1000"));
        }

        return completions;
    }
}
