package me.darkness.wallet.listeners;

import me.darkness.wallet.Main;
import me.darkness.wallet.gui.ConfirmGui;
import me.darkness.wallet.gui.WalletGui;
import me.darkness.wallet.models.Product;
import me.darkness.wallet.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class GuiListener implements Listener {

    private final Main plugin;
    private final Map<UUID, Product> playerProducts = new HashMap<>();

    public GuiListener(Main plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;

        Player player = (Player) event.getWhoClicked();

        if (!isWalletGui(event.getView().getTitle()) && !isConfirmGui(event.getView().getTitle())) {
            return;
        }

        event.setCancelled(true);

        ItemStack clickedItem = event.getCurrentItem();

        if (clickedItem == null || !clickedItem.hasItemMeta()) return;

        ItemMeta meta = clickedItem.getItemMeta();
        if (meta == null || meta.getLore() == null) return;

        List<String> lore = meta.getLore();
        
        String productId = getProductIdFromSlot(event.getSlot());
        if (productId != null) {
            Product product = plugin.getProductManager().getProduct(productId);
            if (product != null) {
                playerProducts.put(player.getUniqueId(), product);
                ConfirmGui confirmGui = new ConfirmGui(plugin, player, product);
                confirmGui.open();
            }
            return;
        }
        
        String action = getActionFromSlot(event.getSlot(), event.getView().getTitle());
        if (action != null) {
            handleAction(player, action, event);
            return;
        }
    }
    
    private String getProductIdFromSlot(int slot) {
        for (Product product : plugin.getProductManager().getAllProducts().values()) {
            if (product.getSlot() == slot) {
                return product.getId();
            }
        }
        return null;
    }
    
    private String getActionFromSlot(int slot, String guiTitle) {
        String configFile = "";
        if (isWalletGui(guiTitle)) {
            configFile = "gui/wallet.yml";
        } else if (isConfirmGui(guiTitle)) {
            configFile = "gui/confirm.yml";
        } else {
            return null;
        }

        var config = plugin.getConfigManager().getConfig(configFile);
        if (config == null) return null;

        var itemsSection = config.getConfigurationSection("items");
        if (itemsSection == null) return null;

        for (String itemKey : itemsSection.getKeys(false)) {
            var itemSection = itemsSection.getConfigurationSection(itemKey);
            if (itemSection != null && itemSection.getInt("slot", -1) == slot) {
                return itemSection.getString("action", null);
            }
        }

        return null;
    }
    
    private void handleAction(Player player, String action, InventoryClickEvent event) {
        switch (action.toUpperCase()) {
            case "CONFIRM":
                handleConfirm(player, event);
                break;
            case "CANCEL":
                handleCancel(player);
                break;
            case "DAILY_REWARD":
                plugin.getDailyRewardManager().claimReward(player);
                break;
            case "NULL":
                break;
        }
    }
    
    private void handleConfirm(Player player, InventoryClickEvent event) {
        Product product = getCurrentProduct(player);
        if (product == null) return;
        
        plugin.getWalletManager().hasBalance(player, product.getPrice()).thenAccept(hasBalance -> {
            if (!hasBalance) {
                MessageUtils.sendMessage(player, plugin.getConfigManager().getConfig("messages.yml")
                        .getString("insufficient-funds", "&cNie masz wystarczajaco VPLN!"));
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
                player.closeInventory();
                return;
            }
            
            plugin.getWalletManager().removeBalance(player, product.getPrice()).thenRun(() -> {
                plugin.getProductManager().executeProductCommands(player, product);

                String purchaseMessage = plugin.getConfigManager().getConfig("messages.yml")
                        .getString("purchase-success", "&aZakupiles {PRODUCT} za {PRICE} VPLN!")
                        .replace("{PRODUCT}", product.getName())
                        .replace("{PRICE}", String.valueOf(product.getPrice()));

                MessageUtils.sendMessage(player, purchaseMessage);

                String globalNotification = plugin.getConfigManager().getConfig("messages.yml")
                        .getString("purchase-notification", "&aGracz &2{PLAYER}&a zakupil &2{PRODUCT}")
                        .replace("{PLAYER}", player.getName())
                        .replace("{PRODUCT}", product.getName());

                String notificationType = plugin.getConfigManager().getConfig("messages.yml")
                        .getString("purchase-notification-type", "bossbar");

                for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                    MessageUtils.sendNotification(onlinePlayer, notificationType, globalNotification);
                }

                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_YES, 1.0f, 1.0f);
                playerProducts.remove(player.getUniqueId());
                player.closeInventory();
            });
        });
    }
    
    private void handleCancel(Player player) {
        WalletGui walletGui = new WalletGui(plugin, player);
        walletGui.open();
    }
    
    private Product getCurrentProduct(Player player) {
        return playerProducts.get(player.getUniqueId());
    }
    
    private boolean isWalletGui(String title) {
        String walletTitle = plugin.getConfigManager().getConfig("gui/wallet.yml")
                .getString("title", "Portfel");
        return MessageUtils.colorize(walletTitle).equals(title);
    }
    
    private boolean isConfirmGui(String title) {
        String confirmTitle = plugin.getConfigManager().getConfig("gui/confirm.yml")
                .getString("title", "Potwierdz zakup");
        return MessageUtils.colorize(confirmTitle).equals(title);
    }
}
