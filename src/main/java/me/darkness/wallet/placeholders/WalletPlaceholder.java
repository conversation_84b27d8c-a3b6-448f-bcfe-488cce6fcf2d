package me.darkness.wallet.placeholders;

import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import me.darkness.wallet.Main;
import org.bukkit.OfflinePlayer;
import org.jetbrains.annotations.NotNull;

public class WalletPlaceholder extends PlaceholderExpansion {
    
    private final Main plugin;
    
    public WalletPlaceholder(Main plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public @NotNull String getIdentifier() {
        return "777-wallet";
    }
    
    @Override
    public @NotNull String getAuthor() {
        return "darkness";
    }
    
    @Override
    public @NotNull String getVersion() {
        return plugin.getDescription().getVersion();
    }
    
    @Override
    public boolean persist() {
        return true;
    }
    
    @Override
    public String onRequest(OfflinePlayer player, @NotNull String params) {
        if (player == null) {
            return "";
        }
        
        if (params.equalsIgnoreCase("money")) {
            try {
                double balance = plugin.getWalletManager().getBalance(player.getUniqueId()).get();
                return plugin.getWalletManager().formatBalance(balance);
            } catch (Exception e) {
                plugin.getLogger().warning("Blad podczas pobierania salda dla placeholdera: " + e.getMessage());
                return "0";
            }
        }
        
        return null;
    }
}
