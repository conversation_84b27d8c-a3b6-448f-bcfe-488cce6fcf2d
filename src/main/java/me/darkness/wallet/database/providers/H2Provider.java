package me.darkness.wallet.database.providers;

import java.sql.*;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class H2<PERSON>rovider implements DatabaseProvider {
    
    private Connection connection;
    private final String connectionString;
    
    public H2Provider(String uri) {
        if (uri.startsWith("jdbc:h2:")) {
            this.connectionString = uri;
        } else {
            this.connectionString = "jdbc:h2:file:" + uri;
        }
    }
    
    @Override
    public void initialize() throws Exception {
        Class.forName("org.h2.Driver");
        connection = DriverManager.getConnection(connectionString);
        createTable();
    }
    
    private void createTable() throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS wallet_players (" +
                "uuid VARCHAR(36) PRIMARY KEY," +
                "balance DOUBLE DEFAULT 0.0" +
                ")";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.executeUpdate();
        }
    }
    
    @Override
    public CompletableFuture<Double> getBalance(UUID playerId) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "SELECT balance FROM wallet_players WHERE uuid = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, playerId.toString());
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    return rs.getDouble("balance");
                }
                return 0.0;
            } catch (SQLException e) {
                e.printStackTrace();
                return 0.0;
            }
        });
    }
    
    @Override
    public CompletableFuture<Void> setBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            String sql = "MERGE INTO wallet_players (uuid, balance) VALUES (?, ?)";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, playerId.toString());
                stmt.setDouble(2, amount);
                stmt.executeUpdate();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        });
    }
    
    @Override
    public CompletableFuture<Void> addBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            String selectSql = "SELECT balance FROM wallet_players WHERE uuid = ?";
            String insertSql = "MERGE INTO wallet_players (uuid, balance) VALUES (?, ?)";
            
            try (PreparedStatement selectStmt = connection.prepareStatement(selectSql)) {
                selectStmt.setString(1, playerId.toString());
                ResultSet rs = selectStmt.executeQuery();
                
                double currentBalance = 0.0;
                if (rs.next()) {
                    currentBalance = rs.getDouble("balance");
                }
                
                try (PreparedStatement insertStmt = connection.prepareStatement(insertSql)) {
                    insertStmt.setString(1, playerId.toString());
                    insertStmt.setDouble(2, currentBalance + amount);
                    insertStmt.executeUpdate();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        });
    }
    
    @Override
    public CompletableFuture<Void> removeBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            String selectSql = "SELECT balance FROM wallet_players WHERE uuid = ?";
            String updateSql = "MERGE INTO wallet_players (uuid, balance) VALUES (?, ?)";
            
            try (PreparedStatement selectStmt = connection.prepareStatement(selectSql)) {
                selectStmt.setString(1, playerId.toString());
                ResultSet rs = selectStmt.executeQuery();
                
                double currentBalance = 0.0;
                if (rs.next()) {
                    currentBalance = rs.getDouble("balance");
                }
                
                double newBalance = Math.max(0, currentBalance - amount);
                
                try (PreparedStatement updateStmt = connection.prepareStatement(updateSql)) {
                    updateStmt.setString(1, playerId.toString());
                    updateStmt.setDouble(2, newBalance);
                    updateStmt.executeUpdate();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        });
    }
    
    @Override
    public void close() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
}
