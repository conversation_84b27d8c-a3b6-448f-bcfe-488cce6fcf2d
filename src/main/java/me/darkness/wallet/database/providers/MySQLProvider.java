package me.darkness.wallet.database.providers;

import java.sql.*;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class MySQLProvider implements DatabaseProvider {
    
    private Connection connection;
    private final String connectionString;
    
    public MySQLProvider(String uri) {
        this.connectionString = uri;
    }
    
    @Override
    public void initialize() throws Exception {
        connection = DriverManager.getConnection(connectionString);
        createTable();
    }
    
    private void createTable() throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS wallet_players (" +
                "uuid VARCHAR(36) PRIMARY KEY," +
                "balance DOUBLE DEFAULT 0.0" +
                ")";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.executeUpdate();
        }
    }
    
    @Override
    public CompletableFuture<Double> getBalance(UUID playerId) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "SELECT balance FROM wallet_players WHERE uuid = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, playerId.toString());
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    return rs.getDouble("balance");
                }
                return 0.0;
            } catch (SQLException e) {
                e.printStackTrace();
                return 0.0;
            }
        });
    }
    
    @Override
    public CompletableFuture<Void> setBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            String sql = "INSERT INTO wallet_players (uuid, balance) VALUES (?, ?) " +
                    "ON DUPLICATE KEY UPDATE balance = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, playerId.toString());
                stmt.setDouble(2, amount);
                stmt.setDouble(3, amount);
                stmt.executeUpdate();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        });
    }
    
    @Override
    public CompletableFuture<Void> addBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            String sql = "INSERT INTO wallet_players (uuid, balance) VALUES (?, ?) " +
                    "ON DUPLICATE KEY UPDATE balance = balance + ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, playerId.toString());
                stmt.setDouble(2, amount);
                stmt.setDouble(3, amount);
                stmt.executeUpdate();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        });
    }
    
    @Override
    public CompletableFuture<Void> removeBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            String sql = "UPDATE wallet_players SET balance = GREATEST(0, balance - ?) WHERE uuid = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setDouble(1, amount);
                stmt.setString(2, playerId.toString());
                stmt.executeUpdate();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        });
    }
    
    @Override
    public void close() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
}
