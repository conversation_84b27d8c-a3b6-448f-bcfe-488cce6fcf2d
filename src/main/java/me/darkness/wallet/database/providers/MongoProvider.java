package me.darkness.wallet.database.providers;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.model.Updates;
import org.bson.Document;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class MongoProvider implements DatabaseProvider {
    
    private MongoClient mongoClient;
    private MongoDatabase database;
    private MongoCollection<Document> collection;
    private final String connectionString;
    private final String databaseName;
    
    public MongoProvider(String uri) {
        this.connectionString = uri;
        this.databaseName = extractDatabaseFromUri(uri);
    }

    private String extractDatabaseFromUri(String uri) {
        try {
            String[] parts = uri.split("/");
            if (parts.length > 3) {
                return parts[3].split("\\?")[0];
            }
        } catch (Exception e) {
        }
        return "wallet";
    }
    
    @Override
    public void initialize() throws Exception {
        mongoClient = MongoClients.create(connectionString);
        database = mongoClient.getDatabase(databaseName);
        collection = database.getCollection("players");
    }
    
    @Override
    public CompletableFuture<Double> getBalance(UUID playerId) {
        return CompletableFuture.supplyAsync(() -> {
            Document doc = collection.find(Filters.eq("uuid", playerId.toString())).first();
            if (doc != null) {
                return doc.getDouble("balance");
            }
            return 0.0;
        });
    }
    
    @Override
    public CompletableFuture<Void> setBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            Document filter = new Document("uuid", playerId.toString());
            Document update = new Document("$set", new Document("balance", amount));
            collection.updateOne(filter, update, new UpdateOptions().upsert(true));
        });
    }
    
    @Override
    public CompletableFuture<Void> addBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            Document filter = new Document("uuid", playerId.toString());
            collection.updateOne(filter, Updates.inc("balance", amount), new UpdateOptions().upsert(true));
        });
    }
    
    @Override
    public CompletableFuture<Void> removeBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            Document doc = collection.find(Filters.eq("uuid", playerId.toString())).first();
            double currentBalance = 0.0;
            if (doc != null) {
                currentBalance = doc.getDouble("balance");
            }
            double newBalance = Math.max(0, currentBalance - amount);
            
            Document filter = new Document("uuid", playerId.toString());
            Document update = new Document("$set", new Document("balance", newBalance));
            collection.updateOne(filter, update, new UpdateOptions().upsert(true));
        });
    }
    
    @Override
    public void close() {
        if (mongoClient != null) {
            mongoClient.close();
        }
    }
}
