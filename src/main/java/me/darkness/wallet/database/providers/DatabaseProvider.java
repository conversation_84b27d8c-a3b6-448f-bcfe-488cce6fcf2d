package me.darkness.wallet.database.providers;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public interface DatabaseProvider {
    
    void initialize() throws Exception;
    
    CompletableFuture<Double> getBalance(UUID playerId);
    
    CompletableFuture<Void> setBalance(UUID playerId, double amount);
    
    CompletableFuture<Void> addBalance(UUID playerId, double amount);
    
    CompletableFuture<Void> removeBalance(UUID playerId, double amount);
    
    void close();
}
