package me.darkness.wallet.database.providers;

import me.darkness.wallet.Main;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class FlatFileProvider implements DatabaseProvider {

    private final Main plugin;
    private File dataFile;
    private FileConfiguration dataConfig;
    
    public FlatFileProvider(Main plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public void initialize() throws Exception {
        dataFile = new File(plugin.getDataFolder(), "data.yml");
        if (!dataFile.exists()) {
            dataFile.createNewFile();
        }
        dataConfig = YamlConfiguration.loadConfiguration(dataFile);
    }
    
    @Override
    public CompletableFuture<Double> getBalance(UUID playerId) {
        return CompletableFuture.supplyAsync(() -> {
            return dataConfig.getDouble("players." + playerId.toString() + ".balance", 0.0);
        });
    }
    
    @Override
    public CompletableFuture<Void> setBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            dataConfig.set("players." + playerId.toString() + ".balance", amount);
            saveData();
        });
    }
    
    @Override
    public CompletableFuture<Void> addBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            double currentBalance = dataConfig.getDouble("players." + playerId.toString() + ".balance", 0.0);
            dataConfig.set("players." + playerId.toString() + ".balance", currentBalance + amount);
            saveData();
        });
    }
    
    @Override
    public CompletableFuture<Void> removeBalance(UUID playerId, double amount) {
        return CompletableFuture.runAsync(() -> {
            double currentBalance = dataConfig.getDouble("players." + playerId.toString() + ".balance", 0.0);
            double newBalance = Math.max(0, currentBalance - amount);
            dataConfig.set("players." + playerId.toString() + ".balance", newBalance);
            saveData();
        });
    }
    
    private void saveData() {
        try {
            dataConfig.save(dataFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Nie mozna zapisac danych gracza!");
            e.printStackTrace();
        }
    }
    
    @Override
    public void close() {
        if (dataConfig != null && dataFile != null) {
            saveData();
        }
    }
}
