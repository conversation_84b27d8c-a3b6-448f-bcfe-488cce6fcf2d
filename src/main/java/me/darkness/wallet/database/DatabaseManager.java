package me.darkness.wallet.database;

import me.darkness.wallet.Main;
import me.darkness.wallet.database.providers.*;
import org.bukkit.configuration.file.FileConfiguration;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class DatabaseManager {
    
    private final Main plugin;
    private DatabaseProvider provider;
    
    public DatabaseManager(Main plugin) {
        this.plugin = plugin;
    }
    
    public void initialize() {
        FileConfiguration config = plugin.getConfig();
        String databaseType = config.getString("database.type", "FLAT").toUpperCase();
        String uri = config.getString("database.uri", "");

        switch (databaseType) {
            case "MYSQL":
                if (uri.isEmpty()) {
                    plugin.getLogger().warning("MySQL URI nie zostalo podane! Uzywam FLAT jako zapasowa opcje.");
                    provider = new FlatFileProvider(plugin);
                } else {
                    provider = new MySQLProvider(uri);
                }
                break;

            case "MONGO":
                if (uri.isEmpty()) {
                    plugin.getLogger().warning("MongoDB URI nie zostalo podane! Uzywam FLAT jako zapasowa opcje.");
                    provider = new FlatFileProvider(plugin);
                } else {
                    provider = new MongoProvider(uri);
                }
                break;



            default:
                provider = new FlatFileProvider(plugin);
                break;
        }
        
        try {
            provider.initialize();
            plugin.getLogger().info("Polaczono z baza danych: " + databaseType);
        } catch (Exception e) {
            plugin.getLogger().severe("Nie mozna polaczyc z baza danych!");
            e.printStackTrace();
            provider = new FlatFileProvider(plugin);
            try {
                provider.initialize();
                plugin.getLogger().info("Uzywam zapasowej bazy danych FLAT");
            } catch (Exception ex) {
                plugin.getLogger().severe("Nie mozna uruchomic zadnej bazy danych!");
                ex.printStackTrace();
            }
        }
    }
    
    public CompletableFuture<Double> getBalance(UUID playerId) {
        return provider.getBalance(playerId);
    }
    
    public CompletableFuture<Void> setBalance(UUID playerId, double amount) {
        return provider.setBalance(playerId, amount);
    }
    
    public CompletableFuture<Void> addBalance(UUID playerId, double amount) {
        return provider.addBalance(playerId, amount);
    }
    
    public CompletableFuture<Void> removeBalance(UUID playerId, double amount) {
        return provider.removeBalance(playerId, amount);
    }
    
    public void close() {
        if (provider != null) {
            provider.close();
        }
    }
}
