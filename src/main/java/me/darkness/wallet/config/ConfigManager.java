package me.darkness.wallet.config;

import me.darkness.wallet.Main;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class ConfigManager {
    
    private final Main plugin;
    private final Map<String, FileConfiguration> configs = new HashMap<>();
    private final Map<String, File> configFiles = new HashMap<>();
    
    public ConfigManager(Main plugin) {
        this.plugin = plugin;
    }
    
    public void loadConfigs() {
        createDefaultConfigs();
        loadConfig("config.yml");
        loadConfig("messages.yml");
        loadConfig("products.yml");
        loadGuiConfigs();
    }
    
    private void createDefaultConfigs() {
        plugin.saveDefaultConfig();
        createConfigIfNotExists("messages.yml");
        createConfigIfNotExists("products.yml");
        createGuiFolder();
        createConfigIfNotExists("gui/wallet.yml");
        createConfigIfNotExists("gui/confirm.yml");
    }
    
    private void createGuiFolder() {
        File guiFolder = new File(plugin.getDataFolder(), "gui");
        if (!guiFolder.exists()) {
            guiFolder.mkdirs();
        }
    }
    
    private void createConfigIfNotExists(String fileName) {
        File file = new File(plugin.getDataFolder(), fileName);
        if (!file.exists()) {
            plugin.saveResource(fileName, false);
        }
    }
    
    private void loadConfig(String fileName) {
        File file = new File(plugin.getDataFolder(), fileName);
        if (file.exists()) {
            FileConfiguration config = YamlConfiguration.loadConfiguration(file);
            configs.put(fileName, config);
            configFiles.put(fileName, file);
        }
    }
    
    private void loadGuiConfigs() {
        File guiFolder = new File(plugin.getDataFolder(), "gui");
        if (guiFolder.exists() && guiFolder.isDirectory()) {
            for (File file : guiFolder.listFiles()) {
                if (file.getName().endsWith(".yml")) {
                    String fileName = "gui/" + file.getName();
                    FileConfiguration config = YamlConfiguration.loadConfiguration(file);
                    configs.put(fileName, config);
                    configFiles.put(fileName, file);
                }
            }
        }
    }
    
    public FileConfiguration getConfig(String fileName) {
        return configs.get(fileName);
    }
    
    public void reloadAllConfigs() {
        plugin.reloadConfig();
        configs.clear();
        loadConfigs();
    }
}
