package me.darkness.wallet.managers;

import me.darkness.wallet.Main;
import me.darkness.wallet.utils.MessageUtils;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

public class DailyRewardManager {

    private final Main plugin;
    private final Random random = new Random();
    private File dataFile;
    private FileConfiguration dataConfig;

    public DailyRewardManager(Main plugin) {
        this.plugin = plugin;
        loadData();
    }

    private void loadData() {
        dataFile = new File(plugin.getDataFolder(), "data.yml");
        if (!dataFile.exists()) {
            try {
                dataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("Nie mozna utworzyc pliku data.yml!");
                e.printStackTrace();
            }
        }
        dataConfig = YamlConfiguration.loadConfiguration(dataFile);
    }
    
    public boolean isEnabled() {
        var config = plugin.getConfigManager().getConfig("gui/wallet.yml");
        if (config == null) return false;

        var itemsSection = config.getConfigurationSection("items");
        if (itemsSection == null) return false;

        return itemsSection.contains("daily_reward");
    }
    
    public boolean canClaim(UUID playerId) {
        if (!isEnabled()) {
            return false;
        }
        
        long lastClaim = dataConfig.getLong("players." + playerId.toString() + ".daily-last-claim", 0);
        long cooldownHours = plugin.getConfig().getLong("daily-reward.cooldown-hours", 24);
        long cooldownMillis = TimeUnit.HOURS.toMillis(cooldownHours);
        
        return System.currentTimeMillis() - lastClaim >= cooldownMillis;
    }
    
    public String getTimeUntilNextClaim(UUID playerId) {
        long lastClaim = dataConfig.getLong("players." + playerId.toString() + ".daily-last-claim", 0);
        long cooldownHours = plugin.getConfig().getLong("daily-reward.cooldown-hours", 24);
        long cooldownMillis = TimeUnit.HOURS.toMillis(cooldownHours);
        
        long timeLeft = cooldownMillis - (System.currentTimeMillis() - lastClaim);
        
        if (timeLeft <= 0) {
            return "0s";
        }
        
        long hours = TimeUnit.MILLISECONDS.toHours(timeLeft);
        long minutes = TimeUnit.MILLISECONDS.toMinutes(timeLeft) % 60;
        long seconds = TimeUnit.MILLISECONDS.toSeconds(timeLeft) % 60;
        
        if (hours > 0) {
            return hours + "h " + minutes + "m";
        } else if (minutes > 0) {
            return minutes + "m " + seconds + "s";
        } else {
            return seconds + "s";
        }
    }
    
    public void claimReward(Player player) {
        if (!isEnabled()) {
            MessageUtils.sendMessage(player, plugin.getConfigManager().getConfig("messages.yml")
                    .getString("daily-reward-disabled", "&cDzienna nagroda jest wylaczona!"));
            return;
        }
        
        if (!canClaim(player.getUniqueId())) {
            String timeLeft = getTimeUntilNextClaim(player.getUniqueId());
            MessageUtils.sendMessage(player, plugin.getConfigManager().getConfig("messages.yml")
                    .getString("daily-reward-cooldown", "&cMusisz poczekac jeszcze {TIME} przed nastepna nagroda!")
                    .replace("{TIME}", timeLeft));
            return;
        }
        
        double minAmount = plugin.getConfig().getDouble("daily-reward.min-amount", 0.01);
        double maxAmount = plugin.getConfig().getDouble("daily-reward.max-amount", 0.15);

        double rewardAmount = minAmount + (maxAmount - minAmount) * random.nextDouble();
        final double finalRewardAmount = Math.round(rewardAmount * 100.0) / 100.0;

        plugin.getWalletManager().addBalance(player, finalRewardAmount).thenRun(() -> {
            dataConfig.set("players." + player.getUniqueId().toString() + ".daily-last-claim", System.currentTimeMillis());
            saveData();

            MessageUtils.sendMessage(player, plugin.getConfigManager().getConfig("messages.yml")
                    .getString("daily-reward-claimed", "&aDzienna nagroda! Otrzymales {AMOUNT} VPLN!")
                    .replace("{AMOUNT}", plugin.getWalletManager().formatBalance(finalRewardAmount)));
        });
    }
    
    private void saveData() {
        try {
            dataConfig.save(dataFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Nie mozna zapisac danych gracza!");
            e.printStackTrace();
        }
    }
    
    public void reloadData() {
        loadData();
    }
}
