package me.darkness.wallet.managers;

import me.darkness.wallet.Main;
import me.darkness.wallet.models.Product;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProductManager {
    
    private final Main plugin;
    private final Map<String, Product> products = new HashMap<>();
    
    public ProductManager(Main plugin) {
        this.plugin = plugin;
        loadProducts();
    }
    
    public void loadProducts() {
        products.clear();
        FileConfiguration config = plugin.getConfigManager().getConfig("products.yml");
        
        if (config == null) {
            plugin.getLogger().warning("Nie mozna zaladowac products.yml!");
            return;
        }
        
        ConfigurationSection productsSection = config.getConfigurationSection("products");
        if (productsSection == null) {
            plugin.getLogger().warning("Brak sekcji 'products' w products.yml!");
            return;
        }
        
        for (String productId : productsSection.getKeys(false)) {
            ConfigurationSection productSection = productsSection.getConfigurationSection(productId);
            if (productSection == null) continue;
            
            String name = productSection.getString("name", productId);
            double price = productSection.getDouble("price", 0.0);
            List<String> commands = productSection.getStringList("commands");
            String material = productSection.getString("material", "STONE");
            List<String> lore = productSection.getStringList("lore");
            int slot = productSection.getInt("slot", 0);
            
            Product product = new Product(productId, name, price, commands, material, lore, slot);
            products.put(productId, product);
        }
    }
    
    public Product getProduct(String id) {
        return products.get(id);
    }
    
    public Map<String, Product> getAllProducts() {
        return new HashMap<>(products);
    }
    
    public void executeProductCommands(Player player, Product product) {
        for (String command : product.getCommands()) {
            String processedCommand = command.replace("{PLAYER}", player.getName())
                    .replace("{UUID}", player.getUniqueId().toString());

            final String finalCommand = processedCommand.startsWith("/") ?
                    processedCommand.substring(1) : processedCommand;

            Bukkit.getScheduler().runTask(plugin, () -> {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), finalCommand);
            });
        }
    }
    
    public Material getMaterial(String materialName) {
        try {
            return Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            return Material.STONE;
        }
    }
}
