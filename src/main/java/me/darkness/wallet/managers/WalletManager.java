package me.darkness.wallet.managers;

import me.darkness.wallet.Main;
import org.bukkit.entity.Player;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class WalletManager {
    
    private final Main plugin;
    
    public WalletManager(Main plugin) {
        this.plugin = plugin;
    }
    
    public CompletableFuture<Double> getBalance(UUID playerId) {
        return plugin.getDatabaseManager().getBalance(playerId);
    }

    public CompletableFuture<Void> setBalance(UUID playerId, double amount) {
        return plugin.getDatabaseManager().setBalance(playerId, Math.max(0, amount));
    }
    
    public CompletableFuture<Void> addBalance(UUID playerId, double amount) {
        if (amount <= 0) {
            return CompletableFuture.completedFuture(null);
        }
        return plugin.getDatabaseManager().addBalance(playerId, amount);
    }
    
    public CompletableFuture<Void> addBalance(Player player, double amount) {
        return addBalance(player.getUniqueId(), amount);
    }
    
    public CompletableFuture<Void> removeBalance(UUID playerId, double amount) {
        if (amount <= 0) {
            return CompletableFuture.completedFuture(null);
        }
        return plugin.getDatabaseManager().removeBalance(playerId, amount);
    }
    
    public CompletableFuture<Void> removeBalance(Player player, double amount) {
        return removeBalance(player.getUniqueId(), amount);
    }
    
    public CompletableFuture<Boolean> hasBalance(UUID playerId, double amount) {
        return getBalance(playerId).thenApply(balance -> balance >= amount);
    }
    
    public CompletableFuture<Boolean> hasBalance(Player player, double amount) {
        return hasBalance(player.getUniqueId(), amount);
    }
    
    public String formatBalance(double balance) {
        if (balance == (long) balance) {
            return String.format("%d", (long) balance);
        } else {
            return String.format("%.2f", balance);
        }
    }
}
