package me.darkness.wallet.utils;

import net.md_5.bungee.api.ChatColor;
import org.bukkit.Bukkit;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitTask;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MessageUtils {
    
    private static final Pattern HEX_PATTERN = Pattern.compile("&#([A-Fa-f0-9]{6})");
    
    public static String colorize(String message) {
        if (message == null) return "";
        
        Matcher matcher = HEX_PATTERN.matcher(message);
        while (matcher.find()) {
            String hexCode = matcher.group(1);
            String hexColor = ChatColor.of("#" + hexCode).toString();
            message = message.replace("&#" + hexCode, hexColor);
        }
        
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    public static void sendMessage(CommandSender sender, String message) {
        if (message == null || message.isEmpty()) return;
        sender.sendMessage(colorize(message));
    }
    
    public static void sendActionBar(Player player, String message) {
        if (message == null || message.isEmpty()) return;
        player.spigot().sendMessage(net.md_5.bungee.api.ChatMessageType.ACTION_BAR, 
                new net.md_5.bungee.api.chat.TextComponent(colorize(message)));
    }
    
    public static void sendTitle(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        if (title == null) title = "";
        if (subtitle == null) subtitle = "";
        player.sendTitle(colorize(title), colorize(subtitle), fadeIn, stay, fadeOut);
    }
    
    public static void sendBossBar(Player player, String message, int duration) {
        if (message == null || message.isEmpty()) return;

        BossBar bossBar = Bukkit.createBossBar(colorize(message), BarColor.GREEN, BarStyle.SOLID);
        bossBar.addPlayer(player);
        bossBar.setProgress(1.0);

        final int totalTicks = duration * 20;
        final int updateInterval = 10;
        final double progressDecrement = 1.0 / (totalTicks / updateInterval);

        new Runnable() {
            private int ticksElapsed = 0;
            private double currentProgress = 1.0;
            private BukkitTask task;

            public void start() {
                task = Bukkit.getScheduler().runTaskTimer(Bukkit.getPluginManager().getPlugin("777-Portfel"), this, updateInterval, updateInterval);
            }

            @Override
            public void run() {
                ticksElapsed += updateInterval;
                currentProgress -= progressDecrement;

                if (currentProgress <= 0 || ticksElapsed >= totalTicks) {
                    bossBar.removePlayer(player);
                    if (task != null) {
                        task.cancel();
                    }
                    return;
                }

                bossBar.setProgress(Math.max(0, currentProgress));
            }
        }.start();
    }
    
    public static void sendNotification(Player player, String type, String message, Object... args) {
        if (message == null || message.isEmpty()) return;
        
        for (int i = 0; i < args.length; i++) {
            message = message.replace("{" + i + "}", String.valueOf(args[i]));
        }
        
        switch (type.toLowerCase()) {
            case "chat":
                sendMessage(player, message);
                break;
            case "actionbar":
                sendActionBar(player, message);
                break;
            case "title":
                String[] parts = message.split("\\|");
                String title = parts.length > 0 ? parts[0] : "";
                String subtitle = parts.length > 1 ? parts[1] : "";
                sendTitle(player, title, subtitle, 10, 70, 20);
                break;
            case "subtitle":
                sendTitle(player, "", message, 10, 70, 20);
                break;
            case "bossbar":
                sendBossBar(player, message, 10);
                break;
        }
    }
}
