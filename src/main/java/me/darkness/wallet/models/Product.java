package me.darkness.wallet.models;

import java.util.List;

public class Product {
    
    private final String id;
    private final String name;
    private final double price;
    private final List<String> commands;
    private final String material;
    private final List<String> lore;
    private final int slot;
    
    public Product(String id, String name, double price, List<String> commands, 
                   String material, List<String> lore, int slot) {
        this.id = id;
        this.name = name;
        this.price = price;
        this.commands = commands;
        this.material = material;
        this.lore = lore;
        this.slot = slot;
    }
    
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public double getPrice() {
        return price;
    }
    
    public List<String> getCommands() {
        return commands;
    }
    
    public String getMaterial() {
        return material;
    }
    
    public List<String> getLore() {
        return lore;
    }
    
    public int getSlot() {
        return slot;
    }
}
