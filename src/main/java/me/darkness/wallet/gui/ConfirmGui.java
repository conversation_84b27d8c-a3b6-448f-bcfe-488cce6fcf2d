package me.darkness.wallet.gui;

import me.darkness.wallet.Main;
import me.darkness.wallet.models.Product;
import me.darkness.wallet.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class ConfirmGui {
    
    private final Main plugin;
    private final Player player;
    private final Product product;
    private Inventory inventory;
    
    public ConfirmGui(Main plugin, Player player, Product product) {
        this.plugin = plugin;
        this.player = player;
        this.product = product;
        createInventory();
    }
    
    private void createInventory() {
        FileConfiguration config = plugin.getConfigManager().getConfig("gui/confirm.yml");
        if (config == null) {
            plugin.getLogger().severe("Nie mozna zaladowac konfiguracji GUI confirm.yml!");
            return;
        }
        
        String title = MessageUtils.colorize(config.getString("title", "Potwierdz zakup"));
        int size = config.getInt("size", 9);
        
        inventory = Bukkit.createInventory(null, size, title);
        
        loadItems(config);
        loadProductInfo();
    }
    
    private void loadItems(FileConfiguration config) {
        ConfigurationSection itemsSection = config.getConfigurationSection("items");
        if (itemsSection == null) return;
        
        for (String itemKey : itemsSection.getKeys(false)) {
            ConfigurationSection itemSection = itemsSection.getConfigurationSection(itemKey);
            if (itemSection == null) continue;
            
            String materialName = itemSection.getString("material", "STONE");
            Material material = plugin.getProductManager().getMaterial(materialName);
            
            int slot = itemSection.getInt("slot", 0);
            String name = MessageUtils.colorize(itemSection.getString("name", ""));
            List<String> lore = new ArrayList<>();
            
            for (String loreLine : itemSection.getStringList("lore")) {
                String processedLine = loreLine
                        .replace("{PRODUCT_NAME}", product.getName())
                        .replace("{PRODUCT_PRICE}", String.valueOf(product.getPrice()))
                        .replace("{PRODUCT_ID}", product.getId());
                lore.add(MessageUtils.colorize(processedLine));
            }
            
            ItemStack item = new ItemStack(material);
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(name);
                meta.setLore(lore);
                meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES, ItemFlag.HIDE_ENCHANTS,
                        ItemFlag.HIDE_UNBREAKABLE, ItemFlag.HIDE_DESTROYS, ItemFlag.HIDE_PLACED_ON);
                item.setItemMeta(meta);
            }
            
            if (slot >= 0 && slot < inventory.getSize()) {
                inventory.setItem(slot, item);
            }
        }
    }
    
    private void loadProductInfo() {
        Material material = plugin.getProductManager().getMaterial(product.getMaterial());
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(MessageUtils.colorize(product.getName()));

            List<String> lore = new ArrayList<>();
            for (String loreLine : product.getLore()) {
                lore.add(MessageUtils.colorize(loreLine
                        .replace("{PRICE}", String.valueOf(product.getPrice()))
                        .replace("{PRODUCT}", product.getId())));
            }

            meta.setLore(lore);
            meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES, ItemFlag.HIDE_ENCHANTS,
                    ItemFlag.HIDE_UNBREAKABLE, ItemFlag.HIDE_DESTROYS, ItemFlag.HIDE_PLACED_ON);
            item.setItemMeta(meta);
        }
        
        int centerSlot = inventory.getSize() / 2;
        inventory.setItem(centerSlot, item);
    }
    
    public void open() {
        if (inventory != null) {
            player.openInventory(inventory);
        }
    }
}
