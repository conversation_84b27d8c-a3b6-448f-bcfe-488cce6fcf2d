package me.darkness.wallet.gui;

import me.clip.placeholderapi.PlaceholderAPI;
import me.darkness.wallet.Main;
import me.darkness.wallet.models.Product;
import me.darkness.wallet.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class WalletGui {
    
    private final Main plugin;
    private final Player player;
    private Inventory inventory;
    
    public WalletGui(Main plugin, Player player) {
        this.plugin = plugin;
        this.player = player;
        createInventory();
    }
    
    private void createInventory() {
        FileConfiguration config = plugin.getConfigManager().getConfig("gui/wallet.yml");
        if (config == null) {
            plugin.getLogger().severe("Nie mozna zaladowac konfiguracji GUI wallet.yml!");
            return;
        }
        
        String title = MessageUtils.colorize(config.getString("title", "Portfel"));
        int size = config.getInt("size", 45);
        
        inventory = Bukkit.createInventory(null, size, title);
        
        loadItems(config);
        loadProducts();
    }
    
    private void loadItems(FileConfiguration config) {
        ConfigurationSection itemsSection = config.getConfigurationSection("items");
        if (itemsSection == null) return;
        
        for (String itemKey : itemsSection.getKeys(false)) {
            ConfigurationSection itemSection = itemsSection.getConfigurationSection(itemKey);
            if (itemSection == null) continue;
            
            String materialName = itemSection.getString("material", "STONE");
            Material material = plugin.getProductManager().getMaterial(materialName);
            
            int slot = itemSection.getInt("slot", 0);
            String name = MessageUtils.colorize(itemSection.getString("name", ""));
            List<String> lore = new ArrayList<>();
            
            for (String loreLine : itemSection.getStringList("lore")) {
                String processedLine = loreLine;
                if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
                    processedLine = PlaceholderAPI.setPlaceholders(player, processedLine);
                }
                lore.add(MessageUtils.colorize(processedLine));
            }
            
            ItemStack item = new ItemStack(material);
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(name);
                meta.setLore(lore);
                meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES, ItemFlag.HIDE_ENCHANTS,
                        ItemFlag.HIDE_UNBREAKABLE, ItemFlag.HIDE_DESTROYS, ItemFlag.HIDE_PLACED_ON);
                item.setItemMeta(meta);
            }
            
            if (slot >= 0 && slot < inventory.getSize()) {
                inventory.setItem(slot, item);
            }
        }
    }
    
    private void loadProducts() {
        Map<String, Product> products = plugin.getProductManager().getAllProducts();
        
        for (Product product : products.values()) {
            Material material = plugin.getProductManager().getMaterial(product.getMaterial());
            ItemStack item = new ItemStack(material);
            ItemMeta meta = item.getItemMeta();
            
            if (meta != null) {
                meta.setDisplayName(MessageUtils.colorize(product.getName()));

                List<String> lore = new ArrayList<>();
                for (String loreLine : product.getLore()) {
                    lore.add(MessageUtils.colorize(loreLine
                            .replace("{PRICE}", String.valueOf(product.getPrice()))
                            .replace("{PRODUCT}", product.getId())));
                }

                meta.setLore(lore);
                meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES, ItemFlag.HIDE_ENCHANTS,
                        ItemFlag.HIDE_UNBREAKABLE, ItemFlag.HIDE_DESTROYS, ItemFlag.HIDE_PLACED_ON);
                item.setItemMeta(meta);
            }
            
            int slot = product.getSlot();
            if (slot >= 0 && slot < inventory.getSize()) {
                inventory.setItem(slot, item);
            }
        }
    }
    
    public void open() {
        if (inventory != null) {
            player.openInventory(inventory);
        }
    }
}
